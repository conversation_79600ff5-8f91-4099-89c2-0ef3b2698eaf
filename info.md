\# OruWebsite - Project Concept \& Development Plan



\## \*\*1. Vision \& Core Concept\*\*



\*\*OruWebsite\*\* is a free, online classifieds platform specifically designed for the Sri Lankan Tamil community, facilitating connections for matrimony, jobs, real estate, and other essential needs. The platform's unique selling proposition is the \*\*"Reference Number System"\*\* that bridges offline advertising (newspapers, community notices, flyers) with structured online response collection.



\*\*Tagline\*\*: "Your Community, Connected"



\## \*\*2. Target Audience\*\*



\### \*\*Primary Users\*\*

\- Sri Lankan Tamils within Sri Lanka and the global diaspora (Canada, UK, Australia, Germany, France, etc.)

\- Age range: 20-60 years

\- Tech-savvy and traditional media users



\### \*\*Secondary Users\*\*

\- Businesses targeting the Sri Lankan Tamil demographic

\- Community organizations and service providers

\- Recruitment agencies and matrimonial services



\## \*\*3. Key User Roles \& Personas\*\*



\### \*\*The Poster (Advertiser)\*\*

\- Creates and publishes classified ads

\- Receives unique reference numbers for hybrid advertising

\- Manages responses through dashboard or email



\### \*\*The Respondent (Interest Party)\*\*

\- Discovers ads through reference numbers or browsing

\- Submits structured responses with relevant details

\- Quick access via reference number lookup



\### \*\*The Admin\*\*

\- Manages platform operations and user content

\- Configures category-specific fields and validation rules

\- Monitors platform health and user engagement



\## \*\*4. Core Features \& Functionality\*\*



\### \*\*A. For Posters (Ad Creation \& Management)\*\*



\#### \*\*User Registration \& Authentication\*\*

\- Simple sign-up: Email/Phone + Password

\- Social login options: Google, Facebook

\- Profile management with basic information



\#### \*\*Smart Ad Posting Workflow\*\*

1\. \*\*Category Selection\*\*: Choose from pre-configured categories

&nbsp;  - Matrimony (திருமணம்)

&nbsp;  - Jobs (வேலைவாய்ப்புகள்)

&nbsp;  - Real Estate (சொத்துக்கள்)

&nbsp;  - For Sale (விற்பனைக்கு)

&nbsp;  - Services (சேவைகள்)

&nbsp;  - Community Events (நிகழ்வுகள்)



2\. \*\*Common Fields\*\* (Mandatory for all ads):

&nbsp;  - Name, Email, Contact Number

&nbsp;  - Preferred contact method



3\. \*\*Dynamic Category Fields\*\* (Auto-loaded based on selection):

&nbsp;  - \*\*Matrimony\*\*: Age, Height, Education, Profession, Caste, Location, Family Background

&nbsp;  - \*\*Jobs\*\*: Company, Position, Salary Range, Experience Required, Location, Qualifications

&nbsp;  - \*\*Real Estate\*\*: Property Type, Square Footage, Price, Location, Amenities



4\. \*\*Custom Response Fields\*\*: Posters can add additional questions specific to their ad

5\. \*\*Preview \& Publish\*\*: Review ad before going live



\#### \*\*Unique Reference Number System\*\*

\- \*\*Format\*\*: Category prefix + unique number (MT125, JB887, RE509)

\- \*\*Properties\*\*: Short, memorable, non-sequential for security

\- \*\*Display\*\*: Prominently shown to poster with copy-to-clipboard functionality

\- \*\*Usage\*\*: Works across online/offline advertising channels



\#### \*\*Ad Management Dashboard\*\*

\- View all active/inactive ads

\- Monitor response statistics

\- Download responses as CSV/PDF

\- Edit or deactivate ads

\- Renewal and promotion options



\### \*\*B. For Respondents (Discovering \& Responding)\*\*



\#### \*\*Homepage Experience\*\*

\- \*\*Primary Feature\*\*: Large reference number search box

&nbsp; - Placeholder: "உங்கள் குறிப்பு எண்ணை உள்ளிடுங்கள் (e.g., MT123)"

\- \*\*Secondary\*\*: Latest ads grid with category filters

\- \*\*Quick Categories\*\*: One-click access to popular categories



\#### \*\*Reference Number Response Flow\*\*

1\. Enter reference number in homepage search

2\. System validates and displays ad summary

3\. Custom response form appears with:

&nbsp;  - Ad details preview

&nbsp;  - Required contact fields

&nbsp;  - Category-specific questions

&nbsp;  - Custom questions set by poster

4\. Form submission with email confirmation



\#### \*\*Alternative Browsing\*\*

\- Category-wise ad listings with search and filters

\- Direct "Respond" buttons on each ad

\- Mobile-optimized infinite scroll



\### \*\*C. Admin Panel (Platform Management)\*\*



\#### \*\*Category \& Field Management\*\*

\- Create/edit/disable ad categories

\- Configure dynamic fields per category:

&nbsp; - Field types: Text, Number, Dropdown, Multi-select, Date, File Upload

&nbsp; - Validation rules and required/optional settings

&nbsp; - Display order and grouping



\#### \*\*Content Moderation\*\*

\- Ad approval workflow (optional)

\- Spam detection and user reporting system

\- Content quality guidelines enforcement



\#### \*\*User Management\*\*

\- User account overview and statistics

\- Role-based access control

\- Communication tools for user support



\#### \*\*Analytics Dashboard\*\*

\- Ad posting trends and category popularity

\- Response rates and user engagement metrics

\- Reference number usage statistics

\- Revenue tracking (future monetization)



\## \*\*5. Hybrid Model Workflow Examples\*\*



\### \*\*Scenario 1: Newspaper Integration\*\*

1\. User posts "Software Engineer Needed" → Gets ref: JB505

2\. Places newspaper ad: "மென்பொருள் பொறியாளர் தேவை! OruWebsite.com இல் JB505 என்ற குறிப்பு எண்ணுடன் விண்ணப்பிக்கவும்"

3\. Candidate sees newspaper → Visits OruWebsite.com → Enters JB505

4\. System shows job details + structured application form

5\. Employer receives organized candidate information



\### \*\*Scenario 2: Community Notice Board\*\*

1\. Family posts matrimony ad → Gets ref: MT301

2\. Prints flyer for temple/community center: "Groom Wanted - Ref: MT301 at OruWebsite.com"

3\. Interested families enter reference number for detailed proposal form



\## \*\*6. Technical Architecture \& Requirements\*\*



\### \*\*Frontend Technology Stack\*\*

\- \*\*Framework\*\*: Next.js (React) for SEO and performance

\- \*\*Styling\*\*: Tailwind CSS with custom Tamil-friendly typography

\- \*\*State Management\*\*: React Context API + React Query

\- \*\*Mobile\*\*: Progressive Web App (PWA) capabilities



\### \*\*Backend Technology Stack\*\*

\- \*\*Runtime\*\*: Node.js with Express.js

\- \*\*Database\*\*: PostgreSQL with flexible JSON fields for custom data

\- \*\*Authentication\*\*: JWT tokens with refresh mechanism

\- \*\*Email Service\*\*: SendGrid or AWS SES for notifications

\- \*\*File Storage\*\*: AWS S3 or Cloudinary for images



\### \*\*Key Technical Features\*\*

\- \*\*Reference Number Generation\*\*: Cryptographically secure, human-readable algorithm

\- \*\*Dynamic Form Builder\*\*: Schema-driven form generation and validation

\- \*\*Multilingual Support\*\*: Tamil and English with easy language switching

\- \*\*Real-time Notifications\*\*: Email alerts for new responses

\- \*\*Security\*\*: Rate limiting, input validation, SQL injection protection



\### \*\*Database Schema Highlights\*\*

```

Categories: id, name, name\_tamil, active, field\_config

Ads: id, user\_id, category\_id, reference\_number, content, custom\_fields, responses\_count

Responses: id, ad\_id, respondent\_data, custom\_responses, created\_at

Users: id, email, phone, name, verified, role

```



\## \*\*7. Monetization Strategy (Future Phases)\*\*



\### \*\*Revenue Streams\*\*

\- \*\*Featured Listings\*\*: Premium placement on homepage and category pages

\- \*\*Highlighted Ads\*\*: Visual emphasis with colored borders/badges

\- \*\*Extended Duration\*\*: Longer ad visibility periods

\- \*\*Premium Analytics\*\*: Detailed response analytics and insights

\- \*\*Bulk Posting\*\*: Subscription plans for businesses and agencies

\- \*\*Verification Badges\*\*: Trusted seller/poster verification system



\### \*\*Pricing Structure\*\* (Projected)

\- Basic ads: Free forever

\- Featured placement: $5-10 per ad

\- Monthly business plans: $20-50 for multiple featured ads

\- Premium analytics: $10/month per advertiser



\## \*\*8. Development Roadmap\*\*



\### \*\*Phase 1: MVP (Months 1-3)\*\*

\*\*Core Functionality\*\*

\- User registration and authentication system

\- Basic categories: Matrimony, Jobs, Real Estate

\- Ad posting with common fields

\- Reference number generation and lookup system

\- Response collection and email notifications

\- Simple homepage with latest ads and reference search

\- Basic admin panel for content moderation



\*\*Success Metrics\*\*: 100 active users, 50 ads posted, 20% response rate



\### \*\*Phase 2: Enhanced Features (Months 4-6)\*\*

\*\*Advanced Capabilities\*\*

\- Dynamic field configuration for categories

\- User dashboard with response management

\- Advanced search and filtering

\- Tamil language support

\- Mobile app (PWA)

\- Email templates and notification preferences

\- Enhanced admin analytics



\*\*Success Metrics\*\*: 500 users, 200 ads, 15% returning users



\### \*\*Phase 3: Monetization \& Scale (Months 7-12)\*\*

\*\*Business Features\*\*

\- Payment gateway integration (Stripe, PayPal)

\- Featured ads and premium placement system

\- Advanced spam protection and moderation tools

\- Social sharing and referral system

\- API for third-party integrations

\- Advanced analytics and reporting



\*\*Success Metrics\*\*: 2000 users, revenue generation, 30% response rate



\### \*\*Phase 4: Community \& Growth (Year 2)\*\*

\*\*Platform Expansion\*\*

\- Community forums and discussion boards

\- Event management system

\- Business directory integration

\- Advanced matching algorithms for matrimony

\- Mobile apps for iOS/Android

\- International expansion features



\## \*\*9. Success Metrics \& KPIs\*\*



\### \*\*User Engagement\*\*

\- Monthly Active Users (MAU)

\- Ad posting frequency

\- Response completion rates

\- Reference number lookup success rate



\### \*\*Platform Health\*\*

\- Average time to first response

\- User retention rates (30, 90, 365 days)

\- Category distribution and popularity

\- Mobile vs desktop usage patterns



\### \*\*Business Metrics\*\* (Future)

\- Revenue per user

\- Conversion rate from free to paid features

\- Customer lifetime value

\- Support ticket resolution time



\## \*\*10. Competitive Advantages\*\*



\### \*\*Unique Differentiators\*\*

\- \*\*Cultural Specificity\*\*: Deep understanding of Sri Lankan Tamil community needs

\- \*\*Hybrid Approach\*\*: Seamless offline-to-online advertising bridge

\- \*\*Reference System\*\*: Innovative solution for cross-media advertising

\- \*\*Community Focus\*\*: Built by and for the community

\- \*\*Structured Responses\*\*: Better than simple contact forms or phone calls



\### \*\*Market Position\*\*

OruWebsite fills the gap between generic classifieds platforms (like Craigslist) and community-specific needs, offering both modern digital convenience and traditional advertising compatibility.



\## \*\*11. Risk Assessment \& Mitigation\*\*



\### \*\*Potential Challenges\*\*

\- \*\*User Adoption\*\*: Educating users about the reference system

\- \*\*Content Quality\*\*: Ensuring legitimate, high-quality ads

\- \*\*Competition\*\*: Established platforms with larger user bases

\- \*\*Technical Scaling\*\*: Managing growth and server costs



\### \*\*Mitigation Strategies\*\*

\- \*\*Community Outreach\*\*: Partner with Tamil organizations and media

\- \*\*Quality Controls\*\*: Automated and manual content moderation

\- \*\*Feature Differentiation\*\*: Focus on unique hybrid approach

\- \*\*Cloud Infrastructure\*\*: Scalable architecture from day one





